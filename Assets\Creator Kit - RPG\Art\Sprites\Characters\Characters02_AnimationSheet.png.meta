fileFormatVersion: 2
guid: 3f198c94b65508c44a32b4b4072bb419
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: <PERSON>_WalkEW_00
  - first:
      213: 21300002
    second: <PERSON>_WalkEW_01
  - first:
      213: 21300004
    second: <PERSON>_WalkEW_02
  - first:
      213: 21300006
    second: <PERSON>_WalkEW_03
  - first:
      213: 21300008
    second: <PERSON>_WalkEW_04
  - first:
      213: 21300010
    second: <PERSON>_WalkEW_05
  - first:
      213: 21300012
    second: <PERSON>_WalkEW_06
  - first:
      213: 21300014
    second: <PERSON>_WalkEW_07
  - first:
      213: 21300016
    second: <PERSON>_WalkS_00
  - first:
      213: 21300018
    second: <PERSON>_WalkS_01
  - first:
      213: 21300020
    second: <PERSON>_WalkS_02
  - first:
      213: 21300022
    second: <PERSON>_WalkS_03
  - first:
      213: 21300024
    second: <PERSON>_WalkS_04
  - first:
      213: 21300026
    second: <PERSON>_WalkS_05
  - first:
      213: 21300028
    second: <PERSON>_WalkS_06
  - first:
      213: 21300030
    second: <PERSON>_WalkS_07
  - first:
      213: 21300032
    second: <PERSON>_WalkN_00
  - first:
      213: 21300034
    second: <PERSON>_WalkN_01
  - first:
      213: 21300036
    second: <PERSON>_WalkN_02
  - first:
      213: 21300038
    second: <PERSON>_WalkN_03
  - first:
      213: 21300040
    second: Warrior_WalkN_04
  - first:
      213: 21300042
    second: Warrior_WalkN_05
  - first:
      213: 21300044
    second: Warrior_WalkN_06
  - first:
      213: 21300046
    second: Warrior_WalkN_07
  - first:
      213: 21300048
    second: _Warrior
  - first:
      213: 21300050
    second: Warrior_Idle_01
  - first:
      213: 21300052
    second: Warrior_Idle_02
  - first:
      213: 21300054
    second: Warrior_Idle_03
  - first:
      213: 21300056
    second: Warrior_Idle_04
  - first:
      213: 21300058
    second: Warrior_Idle_05
  - first:
      213: 21300060
    second: Warrior_Idle_06
  - first:
      213: 21300062
    second: Warrior_Idle_07
  - first:
      213: 21300064
    second: _Villager01
  - first:
      213: 21300066
    second: Villager01_Idle_01
  - first:
      213: 21300068
    second: Villager01_Idle_02
  - first:
      213: 21300070
    second: Villager01_Idle_03
  - first:
      213: 21300072
    second: Villager01_Idle_04
  - first:
      213: 21300074
    second: Villager01_Idle_05
  - first:
      213: 21300076
    second: Villager01_Idle_06
  - first:
      213: 21300078
    second: Villager01_Idle_07
  - first:
      213: 21300080
    second: _Villager02
  - first:
      213: 21300082
    second: Villager02_Idle_01
  - first:
      213: 21300084
    second: Villager02_Idle_02
  - first:
      213: 21300086
    second: Villager02_Idle_03
  - first:
      213: 21300088
    second: Villager02_Idle_04
  - first:
      213: 21300090
    second: Villager02_Idle_05
  - first:
      213: 21300092
    second: Villager02_Idle_06
  - first:
      213: 21300094
    second: Villager02_Idle_07
  - first:
      213: 21300096
    second: _Chicken
  - first:
      213: 21300098
    second: Chicken_Idle_01
  - first:
      213: 21300100
    second: WarriorAndVillagers_AnimationSheet_50
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Warrior_WalkEW_00
      rect:
        serializedVersion: 2
        x: 0
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 46d52e1e581af49a78fa5465c99791c9
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_01
      rect:
        serializedVersion: 2
        x: 64
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2660cc95a4118477b8319b7013c10692
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_02
      rect:
        serializedVersion: 2
        x: 128
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dec397028763d4b25b8f3dff0d20c9c2
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_03
      rect:
        serializedVersion: 2
        x: 192
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fdbdcaa9d8f9c4954a39e2f9818d2ed9
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_04
      rect:
        serializedVersion: 2
        x: 256
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8a40f93cef5f545bcb6802d609d0ad8c
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_05
      rect:
        serializedVersion: 2
        x: 320
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d62094a891f5d4845b6e51f8a4fadf41
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_06
      rect:
        serializedVersion: 2
        x: 384
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 52cacc3b41493483aba4a5e89e7a4fc0
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkEW_07
      rect:
        serializedVersion: 2
        x: 448
        y: 448
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ceb1f0987a5f491497bc7c6da33b51b
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_00
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a91b07347ec3b4465ba2b8a4a7837e94
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_01
      rect:
        serializedVersion: 2
        x: 64
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 94249836334594fdd84861403733fcf6
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_02
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 572797985d76d4bb18abf60fe2f19747
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_03
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7f0443aa8f5f498f877b8f7462c8214
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_04
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8d9b62ab4a1324ca6a26a12b3add59c4
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_05
      rect:
        serializedVersion: 2
        x: 320
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fcc11fb49815a4f31b7e0455980901a8
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_06
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca428432e667a442e9f00a0ba37638d2
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkS_07
      rect:
        serializedVersion: 2
        x: 448
        y: 384
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 99e3dfeb15f8f43b3b4bcb4a1e109df5
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_00
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b71a80cb2b3a4a55999907e0f93af98
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_01
      rect:
        serializedVersion: 2
        x: 64
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d0030765a91d94a118fe2648d31bd011
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_02
      rect:
        serializedVersion: 2
        x: 128
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4239cc4cbe4cd4d2ea102dd79fd18bbb
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_03
      rect:
        serializedVersion: 2
        x: 192
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 265379ac8e7924001b0a811721a84f95
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_04
      rect:
        serializedVersion: 2
        x: 256
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6aa60b94f88994c1a907304f02dfec2a
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_05
      rect:
        serializedVersion: 2
        x: 320
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ff2e11f465cc541a6b36da17c76894f9
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_06
      rect:
        serializedVersion: 2
        x: 384
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c2f3fb27eb57c4eec9a3a2049a07bb77
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_WalkN_07
      rect:
        serializedVersion: 2
        x: 448
        y: 320
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3dc0b991984b248ef87e28423b322868
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: _Warrior
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4abc6c36f66c943c3a6d6d4d9435c7a7
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_01
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 29c1788ccd45c4921a59806180f45034
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_02
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bd2ebcd7488b949c384fe2f62ad7b576
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_03
      rect:
        serializedVersion: 2
        x: 192
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d041547a742894ea4b909c7155c1ffbc
      internalID: 21300054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_04
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d675d3a48ba74efd828f4042f919ede
      internalID: 21300056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_05
      rect:
        serializedVersion: 2
        x: 320
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0636a633310ec40d4856f66390e82896
      internalID: 21300058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_06
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e1c5f684be58044bc933354ff5c7c13b
      internalID: 21300060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Idle_07
      rect:
        serializedVersion: 2
        x: 448
        y: 256
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd162b48cc5f842d78abad06b5408f84
      internalID: 21300062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: _Villager01
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4970262daa3054158bd25486421d4e37
      internalID: 21300064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_01
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9839bc7420f90417daf6d13efc193e5c
      internalID: 21300066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_02
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24fb7405588cd4343ba1eb0ce1d96453
      internalID: 21300068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_03
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2310d82b2a6bc408bab62e7db48374b7
      internalID: 21300070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_04
      rect:
        serializedVersion: 2
        x: 256
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 56a4ea8f285c84ee4b4c5e5951aa81fc
      internalID: 21300072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_05
      rect:
        serializedVersion: 2
        x: 320
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 941b3f52b28d14716a53203076d01bfb
      internalID: 21300074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_06
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b28d6c0dae6044ff786f3b103f98c55e
      internalID: 21300076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager01_Idle_07
      rect:
        serializedVersion: 2
        x: 448
        y: 192
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 94a2235c63a5b492caf9c1e207fe2de0
      internalID: 21300078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: _Villager02
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c50b5d017120744d4bc07f502bf2b0e7
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_01
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7e7bdcaf84a94077a7433c88ab13ca7
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_02
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 26954f40b9df543e394d6746741fae36
      internalID: 21300084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_03
      rect:
        serializedVersion: 2
        x: 192
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1c9742296ba3451884bf187c1cce472
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_04
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 13888406067de489fa53a1c9e594dbc8
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_05
      rect:
        serializedVersion: 2
        x: 320
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c260805d4c367447cac18f00b1b1e68e
      internalID: 21300090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_06
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5966071bdaef140c4ac7bde01af21fcb
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Villager02_Idle_07
      rect:
        serializedVersion: 2
        x: 448
        y: 128
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7eee84ea7cabe4639899b284b5e4cf57
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: _Chicken
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7e72edb33f7ae476198ebdc2835a9803
      internalID: 21300096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Chicken_Idle_01
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 64
        height: 64
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 40e1ddf054e1d4dfcaf772788c22ef8a
      internalID: 21300098
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 3a384a9fdea67184cb6718dbb801eca4
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Warrior_WalkN_01: 21300034
      Warrior_WalkS_00: 21300016
      Warrior_WalkS_06: 21300028
      Warrior_WalkS_04: 21300024
      Villager01_Idle_02: 21300068
      Villager01_Idle_05: 21300074
      Villager02_Idle_07: 21300094
      Warrior_WalkEW_05: 21300010
      Villager02_Idle_04: 21300088
      _Villager01: 21300064
      Villager01_Idle_07: 21300078
      Warrior_Idle_03: 21300054
      Warrior_WalkEW_02: 21300004
      Warrior_WalkS_02: 21300020
      Warrior_Idle_02: 21300052
      Villager01_Idle_03: 21300070
      Villager02_Idle_06: 21300092
      Warrior_WalkEW_01: 21300002
      Warrior_WalkS_03: 21300022
      Warrior_WalkS_01: 21300018
      Villager01_Idle_06: 21300076
      Warrior_WalkN_07: 21300046
      Villager02_Idle_05: 21300090
      _Warrior: 21300048
      Warrior_WalkN_02: 21300036
      Warrior_Idle_06: 21300060
      Warrior_WalkEW_06: 21300012
      Warrior_WalkS_07: 21300030
      Warrior_WalkEW_00: 21300000
      Warrior_Idle_07: 21300062
      Warrior_WalkN_00: 21300032
      Villager02_Idle_01: 21300082
      Warrior_WalkS_05: 21300026
      Villager02_Idle_03: 21300086
      Warrior_WalkEW_07: 21300014
      Villager01_Idle_04: 21300072
      Warrior_WalkEW_03: 21300006
      Villager02_Idle_02: 21300084
      Warrior_Idle_01: 21300050
      Warrior_WalkEW_04: 21300008
      _Villager02: 21300080
      Warrior_Idle_04: 21300056
      Warrior_WalkN_03: 21300038
      Warrior_WalkN_04: 21300040
      _Chicken: 21300096
      Warrior_Idle_05: 21300058
      Warrior_WalkN_06: 21300044
      Villager01_Idle_01: 21300066
      Chicken_Idle_01: 21300098
      Warrior_WalkN_05: 21300042
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
