fileFormatVersion: 2
guid: 4c4775fa3485eda4197558b5d1206d16
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: animated tree2_0
  - first:
      213: 21300002
    second: animated tree2_1
  - first:
      213: 21300004
    second: animated tree2_2
  - first:
      213: 21300006
    second: animated tree2_3
  - first:
      213: 21300008
    second: animated tree2_4
  - first:
      213: 21300010
    second: animated tree2_5
  - first:
      213: 21300012
    second: animated tree2_6
  - first:
      213: 21300014
    second: animated tree2_7
  - first:
      213: 21300016
    second: animated tree2_8
  - first:
      213: 21300018
    second: animated tree2_9
  - first:
      213: 21300020
    second: animated tree2_10
  - first:
      213: 21300022
    second: animated tree2_11
  - first:
      213: 21300024
    second: animated tree2_12
  - first:
      213: 21300026
    second: animated tree2_13
  - first:
      213: 21300028
    second: animated tree2_14
  - first:
      213: 21300030
    second: animated tree2_15
  - first:
      213: 21300032
    second: animated tree2_16
  - first:
      213: 21300034
    second: animated tree2_17
  - first:
      213: 21300036
    second: animated tree2_18
  - first:
      213: 21300038
    second: animated tree2_19
  - first:
      213: 21300040
    second: animated tree2_20
  - first:
      213: 21300042
    second: animated tree2_21
  - first:
      213: 21300044
    second: animated tree2_22
  - first:
      213: 21300046
    second: animated tree2_23
  - first:
      213: 21300048
    second: animated tree2_24
  - first:
      213: 21300050
    second: animated tree2_25
  - first:
      213: 21300052
    second: animated tree2_26
  - first:
      213: 21300054
    second: animated tree2_27
  - first:
      213: 21300056
    second: animated tree2_28
  - first:
      213: 21300058
    second: animated tree2_29
  - first:
      213: 21300060
    second: animated tree2_30
  - first:
      213: 21300062
    second: animated tree2_31
  - first:
      41386430: 2186277476908879412
    second: ImportLogs
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: animated tree2_0
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 178bea883e4a9224f843ce5a80a0704e
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_1
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7708058036b5197469daa81553ba6168
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_2
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2bf657a384900ff4594c3a6ca75f7e8c
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_3
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 548a685fa4fb5354db2d6c12104802e5
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd287e09fc760c1468bf49431cda0d17
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_5
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33623baad5f634245b6b5bc38c15bb87
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_6
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 867f8783f3348b74f92858d08cf0b682
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree2_7
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f1c9c39275b73504280b3da2b67d8c56
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 86af0b09b3f8eb443b2c2a715e09cda8
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      animated tree2_6: 21300012
      animated tree2_3: 21300006
      animated tree2_0: 21300000
      animated tree2_1: 21300002
      animated tree2_7: 21300014
      animated tree2_4: 21300008
      animated tree2_2: 21300004
      animated tree2_5: 21300010
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
