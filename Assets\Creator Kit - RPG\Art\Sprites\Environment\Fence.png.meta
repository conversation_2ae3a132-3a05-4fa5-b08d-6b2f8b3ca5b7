fileFormatVersion: 2
guid: a53d2c44340a6d14ea260ceefba9a1e8
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: Fen<PERSON>_0
  - first:
      213: 21300002
    second: <PERSON><PERSON>_1
  - first:
      213: 21300004
    second: <PERSON><PERSON>_2
  - first:
      213: 21300006
    second: <PERSON><PERSON>_3
  - first:
      213: 21300008
    second: <PERSON><PERSON>_4
  - first:
      213: 21300010
    second: Fen<PERSON>_5
  - first:
      213: 21300012
    second: <PERSON><PERSON>_6
  - first:
      213: 21300014
    second: <PERSON><PERSON>_7
  - first:
      213: 21300016
    second: <PERSON><PERSON>_8
  - first:
      213: 21300018
    second: <PERSON><PERSON>_9
  - first:
      213: 21300020
    second: <PERSON><PERSON>_10
  - first:
      213: 21300022
    second: <PERSON><PERSON>_11
  - first:
      213: 21300024
    second: <PERSON><PERSON>_12
  - first:
      213: 21300026
    second: <PERSON><PERSON>_13
  - first:
      213: 21300028
    second: <PERSON><PERSON>_14
  - first:
      213: 21300030
    second: <PERSON><PERSON>_15
  - first:
      41386430: 2186277476908879412
    second: ImportLogs
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Fence_0
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: 21, y: 15}
        - {x: 8, y: 15}
        - {x: 6, y: 14}
        - {x: 3, y: 32}
        - {x: -4, y: 32}
        - {x: -4, y: -5}
        - {x: -1, y: -15}
        - {x: 30, y: -15}
        - {x: 32, y: -14}
        - {x: 32, y: 13}
      tessellationDetail: 0
      bones: []
      spriteID: e3fafb768e2f24a009b9827788a31d95
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_1
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -32, y: 14}
        - {x: -32, y: -26}
        - {x: -28, y: -32}
        - {x: 4, y: -32}
        - {x: 4, y: 4}
        - {x: 0, y: 14}
      tessellationDetail: 0
      bones: []
      spriteID: a5e0df37bc2dd408db7e6f5cc5baa37f
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_2
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -4, y: 5}
        - {x: -4, y: -32}
        - {x: 16, y: -32}
        - {x: 32, y: -25}
        - {x: 32, y: 14}
        - {x: 0, y: 14}
      tessellationDetail: 0
      bones: []
      spriteID: 47d84ddef674b4b0e99cbece7aad4570
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_3
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -6, y: 32}
        - {x: -32, y: 7}
        - {x: -32, y: -12}
        - {x: -30, y: -15}
        - {x: -1, y: -15}
        - {x: 4, y: -10}
        - {x: 4, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 6fdc09d44557e47a8b2f6897d67bd1fc
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_4
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -31, y: 15}
        - {x: -32, y: 14}
        - {x: -32, y: -16}
        - {x: 31, y: -16}
        - {x: 32, y: -13}
        - {x: 32, y: 12}
        - {x: 27, y: 15}
      - - {x: -18, y: -4}
        - {x: -18, y: -3}
        - {x: -20, y: -3}
        - {x: -20, y: -4}
      tessellationDetail: 0
      bones: []
      spriteID: d19b36c8c87b64fde8d48eea37496851
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_5
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -16, y: 32}
        - {x: -32, y: 19}
        - {x: -32, y: -18}
        - {x: -16, y: -32}
        - {x: 8, y: -32}
        - {x: 32, y: -16}
        - {x: 32, y: 15}
        - {x: 20, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 16664de3d970a4481acfc2f3d59d2390
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_6
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: 7, y: 32}
        - {x: -14, y: 32}
        - {x: -32, y: 18}
        - {x: -32, y: -16}
        - {x: 32, y: -16}
        - {x: 32, y: 7}
        - {x: 26, y: 22}
      tessellationDetail: 0
      bones: []
      spriteID: 14480383090cc4f3d8cfcb093cbfac00
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_7
      rect:
        serializedVersion: 2
        x: 192
        y: 128
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -5, y: 32}
        - {x: -32, y: 17}
        - {x: -32, y: -18}
        - {x: -4, y: -32}
        - {x: 4, y: -32}
        - {x: 4, y: 30}
        - {x: 3, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: f45123dc04b5b4529bb80d7bac68146c
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_8
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: 14, y: 15}
        - {x: 12, y: 14}
        - {x: -3, y: 15}
        - {x: -17, y: 15}
        - {x: -23, y: 12}
        - {x: -26, y: 8}
        - {x: -26, y: -8}
        - {x: -23, y: -16}
        - {x: 20, y: -16}
        - {x: 23, y: -10}
        - {x: 23, y: 5}
        - {x: 17, y: 15}
      tessellationDetail: 0
      bones: []
      spriteID: 021a8c14fbace4ddcb7d7fb61ae85b33
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_9
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -4, y: 32}
        - {x: -4, y: -17}
        - {x: -3, y: -32}
        - {x: 3, y: -32}
        - {x: 4, y: -27}
        - {x: 4, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 1a55c5e568f2f423b9b2711f2cf733b1
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_10
      rect:
        serializedVersion: 2
        x: 128
        y: 64
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -4, y: 32}
        - {x: -4, y: -32}
        - {x: 9, y: -32}
        - {x: 32, y: -22}
        - {x: 32, y: 15}
        - {x: 15, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 316a4eac39fd64a7c96e7bd877db24be
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_11
      rect:
        serializedVersion: 2
        x: 192
        y: 64
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -27, y: 15}
        - {x: -32, y: 11}
        - {x: -32, y: -15}
        - {x: -10, y: -15}
        - {x: -2, y: -12}
        - {x: -2, y: 5}
        - {x: -11, y: 15}
      tessellationDetail: 0
      bones: []
      spriteID: 53fb2b0f6c90b45da9d350bd9476450e
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -3, y: 32}
        - {x: -3, y: 6}
        - {x: 4, y: 6}
        - {x: 5, y: 7}
        - {x: 5, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 585882edbd6d74363beb84dbcbc59bb9
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_13
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -32, y: 15}
        - {x: -32, y: -18}
        - {x: -18, y: -32}
        - {x: 16, y: -32}
        - {x: 32, y: -22}
        - {x: 32, y: 15}
      tessellationDetail: 0
      bones: []
      spriteID: 30d0397792dcc44e89db36817f1c5155
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_14
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: 6, y: 16}
        - {x: 2, y: 11}
        - {x: 2, y: -14}
        - {x: 26, y: -14}
        - {x: 32, y: -12}
        - {x: 32, y: 16}
      tessellationDetail: 0
      bones: []
      spriteID: f9acd7d51ad9a4cc7ba4ffb12d31ad38
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fence_15
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape:
      - - {x: -4, y: -4}
        - {x: -3, y: -32}
        - {x: 3, y: -32}
        - {x: 3, y: -3}
        - {x: 0, y: -2}
        - {x: -2, y: -2}
      tessellationDetail: 0
      bones: []
      spriteID: 0ad16301e3fa741eb8cc13a7c09923d1
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 4f0eecbb88ea5334f8988ab83bdcb67b
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Fence_13: 21300026
      Fence_15: 21300030
      Fence_11: 21300022
      Fence_1: 21300002
      Fence_6: 21300012
      Fence_9: 21300018
      Fence_4: 21300008
      Fence_14: 21300028
      Fence_10: 21300020
      Fence_2: 21300004
      Fence_7: 21300014
      Fence_0: 21300000
      Fence_5: 21300010
      Fence_3: 21300006
      Fence_12: 21300024
      Fence_8: 21300016
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
