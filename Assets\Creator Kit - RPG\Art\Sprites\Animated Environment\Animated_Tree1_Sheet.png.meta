fileFormatVersion: 2
guid: c12a9ef0e75f18949a86a10822cf29f5
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: animated tree_0
  - first:
      213: 21300002
    second: animated tree_1
  - first:
      213: 21300004
    second: animated tree_2
  - first:
      213: 21300006
    second: animated tree_3
  - first:
      213: 21300008
    second: animated tree_4
  - first:
      213: 21300010
    second: animated tree_5
  - first:
      213: 21300012
    second: animated tree_6
  - first:
      213: 21300014
    second: animated tree_7
  - first:
      41386430: 2186277476908879412
    second: ImportLogs
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: animated tree_0
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0a15e211d75a8154fa3c9c0b531346bf
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_1
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 78adee79eb343e245ada0c2a6f7d014d
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_2
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 583eb9f1028fcd84f992d49e97243c04
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_3
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2ababe132b75d5548ae1c22c47e0e384
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0158a701b8d44cf458249cb397a1c1c5
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_5
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 201bac31e1fe81144b44e906afa6fedc
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_6
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ebf422a1e3dae042b8f21ff9e6c1d93
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: animated tree_7
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 8, z: 0, w: 108}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d4fa9a4922996e843abf9b0b023649ff
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: afc352d2cf7ff954a9c3aa179fca4653
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      animated tree_5: 21300010
      animated tree_3: 21300006
      animated tree_1: 21300002
      animated tree_2: 21300004
      animated tree_0: 21300000
      animated tree_7: 21300014
      animated tree_6: 21300012
      animated tree_4: 21300008
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
