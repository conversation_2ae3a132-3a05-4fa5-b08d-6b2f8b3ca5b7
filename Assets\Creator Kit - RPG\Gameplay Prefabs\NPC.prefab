%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6050312672336128433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1933425837913904912}
  - component: {fileID: 2868190722135168810}
  m_Layer: 0
  m_Name: Shadow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1933425837913904912
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6050312672336128433}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.01, y: 0, z: 0}
  m_LocalScale: {x: 0.484887, y: 0.2232345, z: 0.74598}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8835582639856596522}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &2868190722135168810
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6050312672336128433}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e129413db05d94ad28811d643da04b02, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: -1
  m_Sprite: {fileID: 21300016, guid: 4567a16d0e0692048b18bfbb59a5b426, type: 3}
  m_Color: {r: 0, g: 0, b: 0, a: 0.4}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &8115544561518257898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 77084945459706147}
  - component: {fileID: 455141860871801418}
  - component: {fileID: 7228383445418173242}
  - component: {fileID: 5927239570546505319}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &77084945459706147
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8115544561518257898}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.5, y: 1.5, z: 1.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8835582639856596522}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0.5}
  m_SizeDelta: {x: 4.11, y: 0.3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &455141860871801418
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8115544561518257898}
  m_CullTransparentMesh: 1
--- !u!114 &7228383445418173242
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8115544561518257898}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: NPC Name
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 0.2
  m_fontSizeBase: 0.2
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!223 &5927239570546505319
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8115544561518257898}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &8835582639856596533
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8835582639856596522}
  - component: {fileID: 8835582639856596520}
  - component: {fileID: 8835582639856596523}
  - component: {fileID: 8835582639856596521}
  - component: {fileID: 2140298296249417611}
  - component: {fileID: 353955888}
  - component: {fileID: -4612909720202453383}
  - component: {fileID: 8192852867178219724}
  - component: {fileID: 2632425057494222069}
  - component: {fileID: 8906397075621417440}
  - component: {fileID: 995016165121044956}
  - component: {fileID: 771595120165112376}
  - component: {fileID: 487321546485646441}
  - component: {fileID: 2773264044613228601}
  - component: {fileID: 425075453163645739}
  m_Layer: 0
  m_Name: NPC
  m_TagString: NPC
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8835582639856596522
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -8.091, y: 7.96, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1933425837913904912}
  - {fileID: 77084945459706147}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &8835582639856596520
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 22100000, guid: bef255b804d935f4db19d51f39dcb4d0, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!212 &8835582639856596523
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e129413db05d94ad28811d643da04b02, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: -938999163, guid: ********************************, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!61 &8835582639856596521
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0.5}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 0.48, y: 0.72}
    newSize: {x: 1, y: 1}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 1}
  m_EdgeRadius: 0
--- !u!114 &2140298296249417611
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6a6bf3ff12fd4844afaa78b7b2b8b15, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  conversations:
  - {fileID: 353955888}
  conversationTriggerDistance: 1
--- !u!114 &353955888
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: acd99916876a6405980838d7635b65cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  items:
  - id: A
    text: What do you want?
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options:
    - text: Can you tell me what my symbol is?
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: B
      enabled: 0
    - text: Do you want me to tell you what your symbol is?
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: C
      enabled: 0
  - id: B
    text: Sure thing.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options: []
  - id: C
    text: Sure, but you better not lie to me.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options:
    - text: 
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: D
      enabled: 0
  - id: D
    text: Have a look.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options:
    - text: 
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: E
      enabled: 0
  - id: E
    text: Well, what is it?
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options:
    - text: Star.
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: F1
      enabled: 0
    - text: Moon.
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: F2
      enabled: 0
    - text: Sun.
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: F3
      enabled: 0
    - text: Cloud
      image: {fileID: 0}
      audio: {fileID: 0}
      targetId: F4
      enabled: 0
  - id: F1
    text: Thanks, I hope you're telling the truth.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options: []
  - id: F2
    text: Thanks, I hope you're telling the truth.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options: []
  - id: F3
    text: Thanks, I hope you're telling the truth.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options: []
  - id: F4
    text: Thanks, I hope you're telling the truth.
    image: {fileID: 0}
    audio: {fileID: 0}
    quest: {fileID: 0}
    functionRunner: {fileID: 0}
    options: []
--- !u!114 &-4612909720202453383
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5a57f767e5e46a458fc5d3c628d0cbb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  GlobalObjectIdHash: 585143878
  InScenePlacedSourceGlobalObjectIdHash: 0
  AlwaysReplicateAsRoot: 0
  SynchronizeTransform: 1
  ActiveSceneSynchronization: 0
  SceneMigrationSynchronization: 1
  SpawnWithObservers: 1
  DontDestroyWithOwner: 0
  AutoObjectParentSync: 1
--- !u!114 &8192852867178219724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 44955f03bf582984fa156e7540795d47, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  npcSymbol: 0
  isImposter: 0
--- !u!114 &2632425057494222069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 368dfdaf046d0564e9f88d230d84c534, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  npcGuessedRight: 0
  isDead: 0
  npcSymbol: {fileID: 8192852867178219724}
--- !u!114 &8906397075621417440
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d7acb25bec387e43961de6e1f5d5145, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  waypointRoutes:
  - waypointParent: {fileID: 0}
    delay: 0
    nextWaypointParent1: {fileID: 0}
    nextWaypointParent2: {fileID: 0}
    nextWaypointParent3: {fileID: 0}
  - waypointParent: {fileID: 0}
    delay: 0
    nextWaypointParent1: {fileID: 0}
    nextWaypointParent2: {fileID: 0}
    nextWaypointParent3: {fileID: 0}
  - waypointParent: {fileID: 0}
    delay: 0
    nextWaypointParent1: {fileID: 0}
    nextWaypointParent2: {fileID: 0}
    nextWaypointParent3: {fileID: 0}
  - waypointParent: {fileID: 0}
    delay: 5
    nextWaypointParent1: {fileID: 0}
    nextWaypointParent2: {fileID: 0}
    nextWaypointParent3: {fileID: 0}
  speed: 3
  isInConversation:
    m_InternalValue: 0
  flipX:
    m_InternalValue: 0
--- !u!50 &995016165121044956
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 10
  m_LinearDrag: 10
  m_AngularDrag: 0
  m_GravityScale: 0
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 4
--- !u!70 &771595120165112376
CapsuleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: -0.02, y: 0.19}
  m_Size: {x: 0.35, y: 0.01}
  m_Direction: 0
--- !u!114 &487321546485646441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8422fb824ca3c854f9d98f401f94d5b9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseUnreliableDeltas: 0
  SyncPositionX: 1
  SyncPositionY: 1
  SyncPositionZ: 0
  SyncRotAngleX: 0
  SyncRotAngleY: 0
  SyncRotAngleZ: 0
  SyncScaleX: 0
  SyncScaleY: 0
  SyncScaleZ: 0
  PositionThreshold: 0.001
  RotAngleThreshold: 0.01
  ScaleThreshold: 0.01
  UseQuaternionSynchronization: 0
  UseQuaternionCompression: 0
  UseHalfFloatPrecision: 0
  InLocalSpace: 0
  Interpolate: 1
  SlerpPosition: 0
--- !u!114 &2773264044613228601
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a2c67335dee37d9479c8e324c8997fe1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TransitionStateInfoList: []
  m_Animator: {fileID: 8835582639856596520}
--- !u!114 &425075453163645739
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8835582639856596533}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebeeb3598905362428d62da2ee7f1c88, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  npcNameText: {fileID: 7228383445418173242}
