fileFormatVersion: 2
guid: aaa735c5fdc695d4691e911ab4338879
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: water_0
  - first:
      213: 21300002
    second: water_1
  - first:
      213: 21300004
    second: water_2
  - first:
      213: 21300006
    second: water_3
  - first:
      213: 21300008
    second: water_4
  - first:
      213: 21300010
    second: water_5
  - first:
      213: 21300012
    second: water_6
  - first:
      213: 21300014
    second: water_7
  - first:
      213: 21300016
    second: water_8
  - first:
      213: 21300018
    second: water_9
  - first:
      213: 21300020
    second: water_10
  - first:
      213: 21300022
    second: water_11
  - first:
      213: 21300024
    second: water_12
  - first:
      213: 21300026
    second: water_13
  - first:
      213: 21300028
    second: water_14
  - first:
      213: 21300030
    second: water_15
  - first:
      213: 21300032
    second: water_16
  - first:
      213: 21300034
    second: water_17
  - first:
      213: 21300036
    second: water_18
  - first:
      213: 21300038
    second: water_19
  - first:
      213: 21300040
    second: water_20
  - first:
      213: 21300042
    second: water_21
  - first:
      213: 21300044
    second: water_22
  - first:
      213: 21300046
    second: water_23
  - first:
      213: 21300048
    second: water_24
  - first:
      213: 21300050
    second: water_25
  - first:
      213: 21300052
    second: water_26
  - first:
      213: 21300054
    second: water_27
  - first:
      213: 21300056
    second: water_28
  - first:
      213: 21300058
    second: water_29
  - first:
      213: 21300060
    second: water_30
  - first:
      213: 21300062
    second: water_31
  - first:
      213: 21300064
    second: water_32
  - first:
      213: 21300066
    second: water_33
  - first:
      213: 21300068
    second: water_34
  - first:
      213: 21300070
    second: water_35
  - first:
      213: 21300072
    second: water_36
  - first:
      213: 21300074
    second: water_37
  - first:
      213: 21300076
    second: water_38
  - first:
      213: 21300078
    second: water_39
  - first:
      213: 21300080
    second: water_40
  - first:
      213: 21300082
    second: water_41
  - first:
      213: 21300084
    second: water_42
  - first:
      213: 21300086
    second: water_43
  - first:
      213: 21300088
    second: water_44
  - first:
      213: 21300090
    second: water_45
  - first:
      213: 21300092
    second: water_46
  - first:
      213: 21300094
    second: water_47
  - first:
      213: 21300096
    second: water_48
  - first:
      213: 21300098
    second: water_49
  - first:
      213: 21300100
    second: water_50
  - first:
      213: 21300102
    second: water_51
  - first:
      213: 21300104
    second: water_52
  - first:
      213: 21300106
    second: water_53
  - first:
      213: 21300108
    second: water_54
  - first:
      213: 21300110
    second: water_55
  - first:
      213: 21300112
    second: water_56
  - first:
      213: 21300114
    second: water_57
  - first:
      213: 21300116
    second: water_58
  - first:
      213: 21300118
    second: water_59
  - first:
      213: 21300120
    second: water_60
  - first:
      213: 21300122
    second: water_61
  - first:
      213: 21300124
    second: water_62
  - first:
      213: 21300126
    second: water_63
  - first:
      213: 21300128
    second: water_64
  - first:
      213: 21300130
    second: water_65
  - first:
      213: 21300132
    second: water_66
  - first:
      213: 21300134
    second: water_67
  - first:
      213: 21300136
    second: water_68
  - first:
      213: 21300138
    second: water_69
  - first:
      213: 21300140
    second: water_70
  - first:
      213: 21300142
    second: water_71
  - first:
      41386430: 2186277476908879412
    second: ImportLogs
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 0
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: water_0
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d946cffc3cc20b4488b13aa1228df49f
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_1
      rect:
        serializedVersion: 2
        x: 64
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5daccd8f5424d1e4fbeca7563b132576
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_2
      rect:
        serializedVersion: 2
        x: 128
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 886d4b6ab537dca4987630dcf16fe10a
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_3
      rect:
        serializedVersion: 2
        x: 192
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 61aa621384fc45f47b92b8fd5fde0fc4
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_4
      rect:
        serializedVersion: 2
        x: 256
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2fbf4ce695f171c449bf2ecc607cd6a0
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_5
      rect:
        serializedVersion: 2
        x: 320
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71bcc77e1ee9a8548b82d8eab2bd0de0
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_6
      rect:
        serializedVersion: 2
        x: 384
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5684bda1d4f84504f8b6a3851a3d17df
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_7
      rect:
        serializedVersion: 2
        x: 448
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f6d28ce83f66fdb4bb9d8f77dcfdbc65
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_8
      rect:
        serializedVersion: 2
        x: 512
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cb2cf2f5ae45d024888011994b6acf3c
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_9
      rect:
        serializedVersion: 2
        x: 576
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 70175cdee7078364eb5fc0ba402ad496
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_10
      rect:
        serializedVersion: 2
        x: 640
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e5de3df344d84f845bb0e15e349fc126
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_11
      rect:
        serializedVersion: 2
        x: 704
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b5e8ca10cb7059447a23dc178366e7d2
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_12
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 727a881d4a1d42041b1b0295a152ff50
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_13
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d9564bb3039bf843a05a81a0c493760
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_14
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a874f4d1c0f54e04492b06f731c64f7d
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_15
      rect:
        serializedVersion: 2
        x: 192
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 841fb56cc75f66245b8dc6bbca70eedd
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_16
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 404ef893d4eba6140b223db0331de027
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_17
      rect:
        serializedVersion: 2
        x: 320
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 723347280d1103e40aade67583aa1b12
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_18
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: de35ffd49c483874a865e579251bc4b7
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_19
      rect:
        serializedVersion: 2
        x: 448
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8748f8ab46853c9468e9fece74c24c81
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_20
      rect:
        serializedVersion: 2
        x: 512
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 115637f99da1ce343b1dcf0c0c2e49cc
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_21
      rect:
        serializedVersion: 2
        x: 576
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 72541392c351caf4ea1d8aa2699ff1ff
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_22
      rect:
        serializedVersion: 2
        x: 640
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0391ee8d2aabdce42a487e573de0fd5c
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_23
      rect:
        serializedVersion: 2
        x: 704
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f3092150998e56a45ae9a4438694f626
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_24
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5dbfb99c3038f0d4d82ae3c848fa4508
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_25
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8218abcfcebaead439d48b3b9bb6ad10
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_26
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 41b2d4bf0439fc845b866a2a46994fad
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_27
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b5c485ae617de984d8631fe7240f4553
      internalID: 21300054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_28
      rect:
        serializedVersion: 2
        x: 256
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ae0eaa0470dbc3e4f97dd926c7bbd9b8
      internalID: 21300056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_29
      rect:
        serializedVersion: 2
        x: 320
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6aa3641fcadc2a845b596d9e8dd2da61
      internalID: 21300058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_30
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1603458bce565da45acab287afa8b9af
      internalID: 21300060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_31
      rect:
        serializedVersion: 2
        x: 448
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f832ca86a4b56134da0cb359553a0f3c
      internalID: 21300062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_32
      rect:
        serializedVersion: 2
        x: 512
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f68531d45e110e498459c2607f41736
      internalID: 21300064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_33
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eaaa2a919fd715f4587847e8342e4939
      internalID: 21300066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_34
      rect:
        serializedVersion: 2
        x: 640
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c8ace5cc500b892438a976b0f211d24f
      internalID: 21300068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_35
      rect:
        serializedVersion: 2
        x: 704
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8be7e424377eed54288dec6fd0bfd377
      internalID: 21300070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_36
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2966f43dd519be6468345a4ba4a323da
      internalID: 21300072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_37
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3edced3b5d649b94bab3973afeb0f552
      internalID: 21300074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_38
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 72bdf01affdd55a4fb839e1d3792ffab
      internalID: 21300076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_39
      rect:
        serializedVersion: 2
        x: 192
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d62d2e026e26a594c80a35a2dcca8b3e
      internalID: 21300078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_40
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ee33f010fdc7da4294573e340b6b03e
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_41
      rect:
        serializedVersion: 2
        x: 320
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1bed9a6803ed20747b94c563d79667cb
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_42
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0399c0daa124e484b9e51ceb5d0225c9
      internalID: 21300084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_43
      rect:
        serializedVersion: 2
        x: 448
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5255f0e377eba1240b9b48fc1d31c52b
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_44
      rect:
        serializedVersion: 2
        x: 512
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 15cda9ef3b915d449accd90a84a77f5f
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_45
      rect:
        serializedVersion: 2
        x: 576
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 94713ca4140f3a94a804ccfdc7eccda4
      internalID: 21300090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_46
      rect:
        serializedVersion: 2
        x: 640
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d8a7b2a130632d2468b867282ca18db5
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_47
      rect:
        serializedVersion: 2
        x: 704
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 78e0edc28c7d57d488b6271797ddaa7c
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_48
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9786ffadb42bd2f4182a3b36fff8f035
      internalID: 21300096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_49
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: df893b9658cb964449cc8bc96cb13474
      internalID: 21300098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_50
      rect:
        serializedVersion: 2
        x: 128
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 55e775f732464b941b1f4f16af2c0192
      internalID: 21300100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_51
      rect:
        serializedVersion: 2
        x: 192
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bf91a936684db134d96763b111fdb0f8
      internalID: 21300102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_52
      rect:
        serializedVersion: 2
        x: 256
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a0e95da29a046de4fb2b4a92b5459b7c
      internalID: 21300104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_53
      rect:
        serializedVersion: 2
        x: 320
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 590a872a2d251624889c2a4bc9ce2c7e
      internalID: 21300106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_54
      rect:
        serializedVersion: 2
        x: 384
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8a6d302e37f02814d8fd01efd5dc0454
      internalID: 21300108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_55
      rect:
        serializedVersion: 2
        x: 448
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2c6f545362690a64b90051adb2ae9948
      internalID: 21300110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_56
      rect:
        serializedVersion: 2
        x: 512
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 336b33a82b3a4ad4b94bb15cd5720371
      internalID: 21300112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_57
      rect:
        serializedVersion: 2
        x: 576
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 201ee380b71a59444ae53dc909bb58f8
      internalID: 21300114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_58
      rect:
        serializedVersion: 2
        x: 640
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7c88aabe6ec18f442929c9484304261c
      internalID: 21300116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_59
      rect:
        serializedVersion: 2
        x: 704
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 266151c7f3912954b9a938cfd8e08610
      internalID: 21300118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_60
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0fc912b58d7e82b4d8776ff598879242
      internalID: 21300120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_61
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fe67462d5ad4e3f40816be811f11d9a3
      internalID: 21300122
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_62
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9df7b23c271f8fe46a1bd3ecbf381bf3
      internalID: 21300124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_63
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8dab52a397ac7a64cb1ddcf3484a3a6d
      internalID: 21300126
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_64
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bbbaeac2a748328478b1b92d3c360b47
      internalID: 21300128
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_65
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1b6365385508ec499fcd8bc001e5e8c
      internalID: 21300130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_66
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 79f98daadf832a44090e6f21a6eebfeb
      internalID: 21300132
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_67
      rect:
        serializedVersion: 2
        x: 448
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e8b9c287826cf78449c61405f8abe3b5
      internalID: 21300134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_68
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ad0b7f34ad130354eb29b65ceb839135
      internalID: 21300136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_69
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bfdfa7345ae96594ba5e881406e152dc
      internalID: 21300138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_70
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1012fae170803dd49af1d8afc55346e6
      internalID: 21300140
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: water_71
      rect:
        serializedVersion: 2
        x: 704
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 47f0619687f055440ad19f90f4d1a355
      internalID: 21300142
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 9fc2fe2d37785794c86c05db2a5a1618
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      water_1: 21300002
      water_47: 21300094
      water_37: 21300074
      water_51: 21300102
      water_53: 21300106
      water_17: 21300034
      water_43: 21300086
      water_48: 21300096
      water_3: 21300006
      water_56: 21300112
      water_4: 21300008
      water_13: 21300026
      water_20: 21300040
      water_19: 21300038
      water_16: 21300032
      water_9: 21300018
      water_10: 21300020
      water_31: 21300062
      water_36: 21300072
      water_11: 21300022
      water_8: 21300016
      water_7: 21300014
      water_24: 21300048
      water_33: 21300066
      water_58: 21300116
      water_41: 21300082
      water_61: 21300122
      water_60: 21300120
      water_69: 21300138
      water_18: 21300036
      water_34: 21300068
      water_70: 21300140
      water_68: 21300136
      water_2: 21300004
      water_27: 21300054
      water_28: 21300056
      water_46: 21300092
      water_57: 21300114
      water_30: 21300060
      water_5: 21300010
      water_42: 21300084
      water_50: 21300100
      water_35: 21300070
      water_63: 21300126
      water_15: 21300030
      water_62: 21300124
      water_71: 21300142
      water_38: 21300076
      water_0: 21300000
      water_6: 21300012
      water_49: 21300098
      water_64: 21300128
      water_23: 21300046
      water_66: 21300132
      water_65: 21300130
      water_55: 21300110
      water_21: 21300042
      water_29: 21300058
      water_44: 21300088
      water_59: 21300118
      water_25: 21300050
      water_39: 21300078
      water_67: 21300134
      water_52: 21300104
      water_40: 21300080
      water_26: 21300052
      water_32: 21300064
      water_22: 21300044
      water_45: 21300090
      water_14: 21300028
      water_12: 21300024
      water_54: 21300108
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
